"use client";

import React, { useEffect, useState } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp } from '@/lib/utils/environment';
import { logZeroconfMessage } from '@/lib/p2p/zeroconf-discovery';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Play, Square, AlertCircle, CheckCircle, Clock, Search } from 'lucide-react';

/**
 * Autonomous Sync Debug Component
 * 
 * This component provides detailed debugging information for autonomous sync
 * and allows manual control for testing purposes.
 */
export function AutonomousSyncDebug() {
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Initialize autonomous sync with debugging
  const autonomousSync = useAutonomousSync(mainDbInstance, {
    enabled: isMobileApp(),
    discoveryTimeout: 30000, // Shorter timeout for testing
    fallbackDelay: 5000,
    autoSyncDatabases: ['orders', 'staff', 'inventory', 'settings'],
    preferLocalOverInternet: true,
    retryAttempts: 3,
    retryDelay: 10000,
  });

  // Add debug logging
  useEffect(() => {
    const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${message}`;
      setDebugLogs(prev => [...prev.slice(-19), logMessage]); // Keep last 20 logs
      console.log(logMessage);
    };

    // Log environment info
    addLog(`Environment: ${isMobileApp() ? 'Mobile App' : 'Web/Desktop'}`);
    addLog(`Database Ready: ${isReady}`);
    addLog(`Database Instance: ${mainDbInstance ? 'Available' : 'Not Available'}`);

    // Log autonomous sync state changes
    if (autonomousSync.isInitialized) {
      addLog('✅ Autonomous sync initialized');
    }

    if (autonomousSync.isAutonomousActive) {
      addLog('🚀 Autonomous sync is active');
    }

    if (autonomousSync.error) {
      addLog(`❌ Error: ${autonomousSync.error}`);
    }

    const phase = autonomousSync.autonomousState?.phase;
    if (phase) {
      addLog(`📊 Phase: ${phase}`);
    }

    const discoveredCount = autonomousSync.discoveredPeers?.length || 0;
    const connectedCount = autonomousSync.connectedPeers?.length || 0;
    
    if (discoveredCount > 0) {
      addLog(`🔍 Discovered ${discoveredCount} peer(s)`);
    }
    
    if (connectedCount > 0) {
      addLog(`🔗 Connected to ${connectedCount} peer(s)`);
    }

    // Log status message changes
    if (autonomousSync.statusMessage) {
      addLog(`💬 Status: ${autonomousSync.statusMessage}`);
    }
  }, [
    isReady,
    mainDbInstance,
    autonomousSync.isInitialized,
    autonomousSync.isAutonomousActive,
    autonomousSync.error,
    autonomousSync.autonomousState?.phase,
    autonomousSync.discoveredPeers?.length,
    autonomousSync.connectedPeers?.length,
    autonomousSync.statusMessage
  ]);

  // Only show on mobile devices
  if (!isMobileApp()) {
    return null;
  }

  // Toggle visibility
  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-background/80 backdrop-blur-sm"
        >
          <AlertCircle className="h-4 w-4 mr-2" />
          Debug Sync
        </Button>
      </div>
    );
  }

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'discovering': return <Search className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'connecting': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'complete': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'discovering': return 'bg-blue-500';
      case 'connecting': return 'bg-yellow-500';
      case 'complete': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 max-h-96">
      <Card className="bg-background/95 backdrop-blur-sm border shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Autonomous Sync Debug</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Status Overview */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${autonomousSync.isInitialized ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span>Initialized</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${autonomousSync.isAutonomousActive ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span>Active</span>
            </div>
          </div>

          {/* Current Phase */}
          {autonomousSync.autonomousState?.phase && (
            <div className="flex items-center space-x-2">
              {getPhaseIcon(autonomousSync.autonomousState.phase)}
              <Badge variant="outline" className="text-xs">
                {autonomousSync.autonomousState.phase}
              </Badge>
            </div>
          )}

          {/* Peer Counts */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>Discovered: {autonomousSync.discoveredPeers?.length || 0}</div>
            <div>Connected: {autonomousSync.connectedPeers?.length || 0}</div>
          </div>

          {/* Control Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={() => autonomousSync.startAutonomousSync()}
              size="sm"
              variant="outline"
              className="flex-1 text-xs"
              disabled={autonomousSync.isAutonomousActive}
            >
              <Play className="h-3 w-3 mr-1" />
              Start
            </Button>
            <Button
              onClick={() => autonomousSync.stopAutonomousSync()}
              size="sm"
              variant="outline"
              className="flex-1 text-xs"
              disabled={!autonomousSync.isAutonomousActive}
            >
              <Square className="h-3 w-3 mr-1" />
              Stop
            </Button>
            <Button
              onClick={() => autonomousSync.retryConnection()}
              size="sm"
              variant="outline"
              className="flex-1 text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </div>

          {/* Debug Logs */}
          <div className="border rounded p-2 bg-muted/50 max-h-32 overflow-y-auto">
            <div className="text-xs font-medium mb-1">Debug Logs:</div>
            <div className="space-y-1">
              {debugLogs.length === 0 ? (
                <div className="text-xs text-muted-foreground">No logs yet...</div>
              ) : (
                debugLogs.map((log, index) => (
                  <div key={index} className="text-xs font-mono text-muted-foreground">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
