"use client";

import React, { useEffect } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutoStartAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp } from '@/lib/utils/environment';
import { logZeroconfMessage } from '@/lib/p2p/zeroconf-discovery';
import { AutonomousSyncDebug } from '@/components/autonomous-sync-debug';

/**
 * Global Autonomous Sync Provider
 * 
 * This component automatically initializes and starts autonomous P2P sync
 * for mobile devices when the app loads. It ensures that mobile devices
 * immediately start discovering and connecting to desktop hubs without
 * requiring users to visit specific pages.
 * 
 * Key features:
 * - Only runs on mobile devices (Capacitor)
 * - Automatically starts when database is ready
 * - Runs in the background throughout the app lifecycle
 * - Provides seamless autonomous sync experience
 */
export function GlobalAutonomousSyncProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const { db: mainDbInstance, isReady } = useUnifiedDB();

  // Initialize autonomous sync for mobile devices only
  const autonomousSync = useAutoStartAutonomousSync(mainDbInstance, {
    enabled: isMobileApp(), // Only enable on mobile devices
    discoveryTimeout: 60000, // 60 seconds to discover peers
    fallbackDelay: 5000, // 5 seconds before trying internet fallback
    autoSyncDatabases: ['orders', 'staff', 'inventory', 'settings'],
    preferLocalOverInternet: true, // Prefer local mDNS over internet
    retryAttempts: 5, // More retries for better reliability
    retryDelay: 15000, // 15 seconds between retries
  });

  // Log autonomous sync status for debugging
  useEffect(() => {
    if (!isMobileApp()) {
      return; // Only log for mobile devices
    }

    if (isReady && mainDbInstance) {
      logZeroconfMessage('🌐 Global autonomous sync provider: Database ready, autonomous sync should start');
    }

    if (autonomousSync.isInitialized) {
      logZeroconfMessage('🌐 Global autonomous sync provider: Autonomous sync initialized');
    }

    if (autonomousSync.isAutonomousActive) {
      logZeroconfMessage('🌐 Global autonomous sync provider: Autonomous sync is active');
    }

    if (autonomousSync.error) {
      logZeroconfMessage(`🌐 Global autonomous sync provider: Error - ${autonomousSync.error}`);
    }

    // Log current status
    const status = autonomousSync.autonomousState?.phase || 'unknown';
    const connectedPeers = autonomousSync.connectedPeers?.length || 0;
    const discoveredPeers = autonomousSync.discoveredPeers?.length || 0;
    
    logZeroconfMessage(
      `🌐 Global autonomous sync status: ${status}, ` +
      `discovered: ${discoveredPeers}, connected: ${connectedPeers}`
    );
  }, [
    isReady,
    mainDbInstance,
    autonomousSync.isInitialized,
    autonomousSync.isAutonomousActive,
    autonomousSync.error,
    autonomousSync.autonomousState?.phase,
    autonomousSync.connectedPeers?.length,
    autonomousSync.discoveredPeers?.length
  ]);

  // Log when autonomous sync starts discovering
  useEffect(() => {
    if (!isMobileApp()) return;

    if (autonomousSync.autonomousState?.phase === 'discovering') {
      logZeroconfMessage('🔍 Global autonomous sync: Started discovering desktop hubs...');
    }
    
    if (autonomousSync.autonomousState?.phase === 'connecting') {
      logZeroconfMessage('🔗 Global autonomous sync: Connecting to discovered hubs...');
    }
    
    if (autonomousSync.autonomousState?.phase === 'complete') {
      logZeroconfMessage('✅ Global autonomous sync: Successfully connected and syncing!');
    }
    
    if (autonomousSync.autonomousState?.phase === 'error') {
      logZeroconfMessage(`❌ Global autonomous sync: Error - ${autonomousSync.autonomousState.error}`);
    }
  }, [autonomousSync.autonomousState?.phase, autonomousSync.autonomousState?.error]);

  // Log peer discovery events
  useEffect(() => {
    if (!isMobileApp()) return;

    const discoveredCount = autonomousSync.discoveredPeers?.length || 0;
    const connectedCount = autonomousSync.connectedPeers?.length || 0;

    if (discoveredCount > 0) {
      logZeroconfMessage(`🔍 Global autonomous sync: Discovered ${discoveredCount} desktop hub(s)`);
      autonomousSync.discoveredPeers?.forEach((peer, index) => {
        logZeroconfMessage(`  Hub ${index + 1}: ${peer.hostname} (${peer.ipAddress}:${peer.port})`);
      });
    }

    if (connectedCount > 0) {
      logZeroconfMessage(`🔗 Global autonomous sync: Connected to ${connectedCount} desktop hub(s)`);
      autonomousSync.connectedPeers?.forEach((peer, index) => {
        logZeroconfMessage(`  Connected Hub ${index + 1}: ${peer.hostname} (${peer.ipAddress}:${peer.port})`);
      });
    }
  }, [autonomousSync.discoveredPeers?.length, autonomousSync.connectedPeers?.length]);

  // The provider doesn't render any UI, it just manages autonomous sync in the background
  return (
    <>
      {children}
      <AutonomousSyncDebug />
    </>
  );
}
